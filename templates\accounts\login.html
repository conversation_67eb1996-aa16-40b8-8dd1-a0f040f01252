{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}تسجيل الدخول{% endblock %}

{% block extra_css %}
<style>
    /* Login page specific styles */
    .login-container {
        min-height: 100vh;
        background: var(--gradient-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.2) 0%, transparent 50%);
        z-index: 1;
    }

    .login-container > .container {
        position: relative;
        z-index: 2;
    }

    /* Login Card Styles */
    .login-container .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        border-radius: 20px;
        box-shadow: var(--shadow-blue);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .login-container .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(59, 130, 246, 0.3);
    }

    .login-container .card-header {
        background: var(--gradient-primary);
        border: none;
        padding: 2rem 1.5rem;
        position: relative;
    }

    /* Logo Styles */
    .login-logo {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        position: relative;
        overflow: hidden;
    }

    .login-logo img {
        width: 60px;
        height: 60px;
        position: relative;
        z-index: 1;
    }

    .login-title {
        color: white !important;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 0.5rem;
    }

    .login-subtitle {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 400;
        margin-bottom: 1rem;
    }

    .login-form-title {
        color: white !important;
        font-weight: 600;
        margin-top: 1rem;
    }

    /* Login Form Styles */
    .login-container .card-body {
        padding: 2rem;
        background: white;
    }

    .login-container .form-group {
        margin-bottom: 1.5rem;
    }

    .login-container .form-control {
        border: 2px solid var(--border-light);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: var(--bg-light);
        color: var(--text-dark);
    }

    .login-container .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        background: white;
    }

    .login-container label {
        color: var(--text-dark);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .login-container .btn-primary {
        background: var(--gradient-primary);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .login-container .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }

    .login-container .alert-danger {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        border: 1px solid #f87171;
        color: #dc2626;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* Divider Style */
    .login-container hr {
        border: none;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
        margin: 1rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-header text-center">
                        <!-- Logo -->
                        <div class="login-logo">
                            <img src="{% static 'images/logo.svg' %}" alt="الإشرافي" style="width: 60px; height: 60px;">
                        </div>

                        <!-- Title -->
                        <h2 class="login-title">الإشرافي</h2>
                        <h5 class="login-subtitle">نظام المحاسبة المتقدم</h5>

                        <!-- Divider -->
                        <hr>

                        <!-- Form Title -->
                        <h4 class="login-form-title">تسجيل الدخول</h4>
                    </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                اسم المستخدم أو كلمة المرور غير صحيحة
                            </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}