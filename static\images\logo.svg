<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#1e3a8a" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" filter="url(#shadow)"/>
  
  <!-- Calculator Icon -->
  <g transform="translate(25, 20)">
    <!-- Calculator Body -->
    <rect x="5" y="5" width="40" height="50" rx="4" ry="4" fill="url(#iconGradient)" stroke="none"/>
    
    <!-- Screen -->
    <rect x="10" y="10" width="30" height="12" rx="2" ry="2" fill="#1e3a8a" opacity="0.8"/>
    
    <!-- Screen Text -->
    <text x="25" y="19" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white" font-weight="bold">123</text>
    
    <!-- Buttons Row 1 -->
    <circle cx="15" cy="30" r="3" fill="#3b82f6"/>
    <circle cx="25" cy="30" r="3" fill="#3b82f6"/>
    <circle cx="35" cy="30" r="3" fill="#3b82f6"/>
    
    <!-- Buttons Row 2 -->
    <circle cx="15" cy="40" r="3" fill="#3b82f6"/>
    <circle cx="25" cy="40" r="3" fill="#3b82f6"/>
    <circle cx="35" cy="40" r="3" fill="#3b82f6"/>
    
    <!-- Buttons Row 3 -->
    <circle cx="15" cy="50" r="3" fill="#3b82f6"/>
    <circle cx="25" cy="50" r="3" fill="#3b82f6"/>
    <circle cx="35" cy="50" r="3" fill="#60a5fa"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="20" cy="25" r="2" fill="white" opacity="0.3"/>
  <circle cx="75" cy="35" r="1.5" fill="white" opacity="0.4"/>
  <circle cx="80" cy="70" r="1" fill="white" opacity="0.5"/>
</svg>
