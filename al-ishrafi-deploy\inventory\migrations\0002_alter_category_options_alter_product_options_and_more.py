# Generated by Django 5.1.6 on 2025-03-02 22:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='category',
            options={'ordering': ['code'], 'verbose_name': 'تصنيف', 'verbose_name_plural': 'التصنيفات'},
        ),
        migrations.AlterModelOptions(
            name='product',
            options={'ordering': ['code'], 'verbose_name': 'منتج', 'verbose_name_plural': 'المنتجات'},
        ),
        migrations.AlterModelOptions(
            name='stockmovement',
            options={'ordering': ['-date', '-created_at'], 'verbose_name': 'حركة مخزنية', 'verbose_name_plural': 'حركات المخزن'},
        ),
        migrations.AlterModelOptions(
            name='stocktaking',
            options={'ordering': ['-date'], 'verbose_name': 'جرد مخزني', 'verbose_name_plural': 'عمليات الجرد'},
        ),
        migrations.AlterModelOptions(
            name='stocktakingline',
            options={'verbose_name': 'بند جرد', 'verbose_name_plural': 'بنود الجرد'},
        ),
        migrations.AlterModelOptions(
            name='warehouse',
            options={'ordering': ['code'], 'verbose_name': 'مخزن', 'verbose_name_plural': 'المخازن'},
        ),
        migrations.AlterField(
            model_name='category',
            name='code',
            field=models.CharField(max_length=10, unique=True, verbose_name='كود التصنيف'),
        ),
        migrations.AlterField(
            model_name='category',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='category',
            name='description',
            field=models.TextField(blank=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='category',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='category',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم التصنيف'),
        ),
        migrations.AlterField(
            model_name='category',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='inventory.category', verbose_name='التصنيف الرئيسي'),
        ),
        migrations.AlterField(
            model_name='category',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='product',
            name='barcode',
            field=models.CharField(blank=True, max_length=50, verbose_name='باركود'),
        ),
        migrations.AlterField(
            model_name='product',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='inventory.category', verbose_name='التصنيف'),
        ),
        migrations.AlterField(
            model_name='product',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='كود المنتج'),
        ),
        migrations.AlterField(
            model_name='product',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='product',
            name='current_stock',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المخزون الحالي'),
        ),
        migrations.AlterField(
            model_name='product',
            name='description',
            field=models.TextField(blank=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image',
            field=models.ImageField(blank=True, upload_to='products/', verbose_name='صورة المنتج'),
        ),
        migrations.AlterField(
            model_name='product',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='product',
            name='min_stock',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى للمخزون'),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(max_length=200, verbose_name='اسم المنتج'),
        ),
        migrations.AlterField(
            model_name='product',
            name='purchase_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الشراء'),
        ),
        migrations.AlterField(
            model_name='product',
            name='sale_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر البيع'),
        ),
        migrations.AlterField(
            model_name='product',
            name='unit',
            field=models.CharField(choices=[('piece', 'قطعة'), ('kg', 'كيلوجرام'), ('m', 'متر'), ('l', 'لتر')], max_length=10, verbose_name='وحدة القياس'),
        ),
        migrations.AlterField(
            model_name='product',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='date',
            field=models.DateField(verbose_name='تاريخ الحركة'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='movement_type',
            field=models.CharField(choices=[('in', 'وارد'), ('out', 'منصرف')], max_length=3, verbose_name='نوع الحركة'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to='inventory.product', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='reason',
            field=models.CharField(choices=[('purchase', 'شراء'), ('sale', 'بيع'), ('return_in', 'مرتجع وارد'), ('return_out', 'مرتجع منصرف'), ('adjustment', 'تسوية مخزن'), ('transfer', 'تحويل مخزني')], max_length=20, verbose_name='سبب الحركة'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='reference',
            field=models.CharField(max_length=50, verbose_name='المرجع'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_movements', to='inventory.warehouse', verbose_name='المخزن'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktakings', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='date',
            field=models.DateField(verbose_name='تاريخ الجرد'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('in_progress', 'جاري الجرد'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='stocktaking',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktakings', to='inventory.warehouse', verbose_name='المخزن'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='actual_quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية الفعلية'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='difference',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الفرق'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='expected_quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية المتوقعة'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stocktaking_lines', to='inventory.product', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='stocktakingline',
            name='stocktaking',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='inventory.stocktaking', verbose_name='الجرد'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='code',
            field=models.CharField(max_length=10, unique=True, verbose_name='كود المخزن'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='location',
            field=models.TextField(verbose_name='الموقع'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='manager',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم المخزن'),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
    ]
