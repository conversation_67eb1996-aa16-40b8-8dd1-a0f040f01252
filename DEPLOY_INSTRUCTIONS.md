# 🚀 تعليمات نشر نظام الإشرافي على Firebase

## ⚠️ متطلب مهم: تثبيت Google Cloud SDK

### 1. تحميل وتثبيت Google Cloud SDK

#### على Windows:
1. اذهب إلى: https://cloud.google.com/sdk/docs/install-sdk
2. حمل "Google Cloud CLI installer for Windows"
3. شغل الملف المحمل واتبع التعليمات
4. أعد تشغيل Command Prompt أو PowerShell

#### أو استخدم PowerShell:
```powershell
# تشغيل PowerShell كمدير
(New-Object Net.WebClient).DownloadFile("https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe", "$env:Temp\GoogleCloudSDKInstaller.exe")
& $env:Temp\GoogleCloudSDKInstaller.exe
```

### 2. التحقق من التثبيت
```bash
gcloud --version
```

### 3. تسجيل الدخول وإعداد المشروع
```bash
# تسجيل الدخول
gcloud auth login

# إنشاء مشروع جديد (اختياري)
gcloud projects create al-ishrafi-accounting --name="Al-Ishrafi Accounting"

# تعيين المشروع
gcloud config set project al-ishrafi-accounting

# إنشاء تطبيق App Engine
gcloud app create --region=us-central1
```

### 4. النشر
```bash
# الطريقة الأولى: استخدام السكريبت
python quick_deploy.py

# الطريقة الثانية: النشر اليدوي
gcloud app deploy app.yaml --quiet

# الطريقة الثالثة: على Windows
deploy.bat
```

## 🎯 خطوات مفصلة للنشر

### الخطوة 1: إعداد Google Cloud
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعل App Engine API
4. فعل Cloud Build API

### الخطوة 2: تحديث app.yaml
```yaml
runtime: python39

env_variables:
  SECRET_KEY: "al-ishrafi-production-secret-key-2025-CHANGE-THIS"
  DEBUG: "False"
  ALLOWED_HOSTS: "*"
  DATABASE_URL: "sqlite:///db.sqlite3"
```

### الخطوة 3: النشر
```bash
# جمع الملفات الثابتة
python manage.py collectstatic --noinput

# تشغيل الهجرات
python manage.py migrate

# إنشاء مستخدم إداري
python manage.py create_admin

# النشر
gcloud app deploy app.yaml --quiet
```

### الخطوة 4: اختبار التطبيق
```bash
# فتح التطبيق في المتصفح
gcloud app browse

# أو زيارة الرابط يدوياً
# https://YOUR_PROJECT_ID.appspot.com
```

## 🔧 استكشاف الأخطاء

### مشكلة: "gcloud command not found"
**الحل:**
1. تأكد من تثبيت Google Cloud SDK
2. أعد تشغيل Terminal/Command Prompt
3. أضف مسار gcloud إلى PATH

### مشكلة: "Permission denied"
**الحل:**
```bash
gcloud auth login
gcloud auth application-default login
```

### مشكلة: "Project not found"
**الحل:**
```bash
gcloud config set project YOUR_PROJECT_ID
gcloud app create --region=us-central1
```

### مشكلة: "Static files not found"
**الحل:**
```bash
python manage.py collectstatic --noinput
```

## 💰 التكلفة

### النشر المجاني:
- App Engine: 28 ساعة مجانية يومياً
- قاعدة بيانات SQLite: مجانية
- التخزين: 5GB مجاني

### النشر الاحترافي:
- App Engine: $5-15/شهر
- Cloud SQL: $7-20/شهر
- التخزين: $1-5/شهر

## 🎉 بعد النشر الناجح

### 1. الوصول للتطبيق:
- الرابط: `https://YOUR_PROJECT_ID.appspot.com`
- تسجيل الدخول: `admin` / `admin123`

### 2. إعدادات إضافية:
```bash
# ربط نطاق مخصص
gcloud app domain-mappings create yourdomain.com

# مراقبة السجلات
gcloud app logs tail -s default

# تحديث التطبيق
gcloud app deploy --version=v2
```

### 3. الأمان:
- غير كلمة مرور المدير
- حدث SECRET_KEY في app.yaml
- فعل Cloud SQL للإنتاج

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من [وثائق Google Cloud](https://cloud.google.com/appengine/docs)
2. راجع [أمثلة Django على App Engine](https://github.com/GoogleCloudPlatform/python-docs-samples/tree/main/appengine/standard_python3/django)
3. استخدم [Stack Overflow](https://stackoverflow.com/questions/tagged/google-app-engine+django)

---

## ✅ قائمة التحقق النهائية

- [ ] تثبيت Google Cloud SDK
- [ ] تسجيل الدخول: `gcloud auth login`
- [ ] إنشاء/تعيين المشروع
- [ ] تحديث SECRET_KEY في app.yaml
- [ ] تشغيل: `python manage.py collectstatic --noinput`
- [ ] تشغيل: `gcloud app deploy app.yaml --quiet`
- [ ] اختبار التطبيق على الرابط المعطى

**🎯 بعد اتباع هذه الخطوات، سيكون نظام الإشرافي متاحاً على الإنترنت!**
