# 🚀 النشر التلقائي عبر GitHub Actions

## المميزات:
- ✅ نشر تلقائي عند كل push
- ✅ لا يحتاج تثبيت Google Cloud SDK محلياً
- ✅ نشر آمن باستخدام Service Account
- ✅ سجلات مفصلة لكل عملية نشر

## الإعداد (مرة واحدة فقط):

### 1. إنشاء Service Account في Google Cloud

```bash
# إنشاء Service Account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions" \
    --display-name="GitHub Actions"

# إعطاء الصلاحيات المطلوبة
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/appengine.deployer"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudbuild.builds.editor"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# إنشاء مفتاح JSON
gcloud iam service-accounts keys create key.json \
    --iam-account=github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### 2. إضافة Secrets في GitHub

اذهب إلى GitHub Repository → Settings → Secrets and variables → Actions

أضف هذه الـ Secrets:

1. **GCP_SA_KEY**: محتوى ملف `key.json` كاملاً
2. **GCP_PROJECT_ID**: معرف مشروع Google Cloud

### 3. رفع الكود إلى GitHub

```bash
git add .
git commit -m "Add Firebase deployment configuration"
git push origin main
```

## 🎯 كيفية النشر:

### النشر التلقائي:
- كل push إلى branch main سيؤدي إلى نشر تلقائي
- تابع التقدم في GitHub Actions tab

### النشر اليدوي:
1. اذهب إلى GitHub Repository
2. Actions tab
3. اختر "Deploy to Google App Engine"
4. اضغط "Run workflow"

## 📊 مراقبة النشر:

### في GitHub:
- Actions tab → آخر workflow run
- شاهد السجلات المفصلة لكل خطوة

### في Google Cloud:
```bash
# مراقبة السجلات
gcloud app logs tail -s default

# حالة التطبيق
gcloud app versions list
```

## 🔧 تخصيص النشر:

### تغيير branch النشر:
```yaml
# في .github/workflows/deploy.yml
on:
  push:
    branches: [ production ]  # بدلاً من main
```

### إضافة اختبارات قبل النشر:
```yaml
- name: Run tests
  run: |
    python manage.py test
```

### نشر على environments مختلفة:
```yaml
# إضافة environment للإنتاج
environment: production
```

## 🛡️ الأمان:

### أفضل الممارسات:
- ✅ استخدام Service Account بدلاً من مفاتيح المستخدم
- ✅ صلاحيات محدودة للـ Service Account
- ✅ تشفير المفاتيح في GitHub Secrets
- ✅ مراجعة السجلات بانتظام

### تدوير المفاتيح:
```bash
# حذف المفتاح القديم
gcloud iam service-accounts keys delete KEY_ID \
    --iam-account=github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com

# إنشاء مفتاح جديد
gcloud iam service-accounts keys create new-key.json \
    --iam-account=github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

## 🎉 بعد النشر الناجح:

### التحقق من النشر:
1. زيارة: `https://YOUR_PROJECT_ID.appspot.com`
2. تسجيل الدخول: `admin` / `admin123`
3. اختبار الوظائف الأساسية

### إعدادات إضافية:
- ربط نطاق مخصص
- إعداد SSL certificate
- تكوين Cloud SQL للإنتاج

## 📞 استكشاف الأخطاء:

### فشل في Authentication:
- تحقق من صحة GCP_SA_KEY
- تأكد من صلاحيات Service Account

### فشل في النشر:
- تحقق من app.yaml
- راجع سجلات GitHub Actions
- تأكد من تفعيل App Engine API

### مشاكل في التطبيق:
```bash
# عرض السجلات
gcloud app logs tail -s default

# إعادة النشر
git commit --allow-empty -m "Redeploy"
git push origin main
```

---

## ✅ قائمة التحقق:

- [ ] إنشاء Service Account في Google Cloud
- [ ] إضافة Secrets في GitHub
- [ ] رفع الكود إلى GitHub
- [ ] تشغيل GitHub Action
- [ ] اختبار التطبيق المنشور

**🎯 الآن يمكنك النشر بسهولة عبر GitHub Actions!**
