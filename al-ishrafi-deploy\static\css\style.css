/* Arabic font import */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* Blue and White Theme Variables */
:root {
    --bg-primary: #0f1419;
    --bg-secondary: #1e3a8a;
    --bg-tertiary: #3b82f6;
    --bg-light: #f8fafc;
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --text-dark: #1e293b;
    --accent-color: #3b82f6;
    --accent-light: #60a5fa;
    --accent-dark: #1d4ed8;
    --border-color: #334155;
    --border-light: #e2e8f0;
    --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.3);
    --shadow-light: 0 2px 10px rgba(59, 130, 246, 0.1);
    --shadow-blue: 0 4px 20px rgba(59, 130, 246, 0.2);
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    --gradient-secondary: linear-gradient(135deg, #0f1419 0%, #1e3a8a 100%);
}

/* General styles */
body {
    font-family: 'Cairo', sans-serif;
    background: var(--gradient-secondary);
    color: var(--text-primary);
    min-height: 100vh;
}

/* RTL specific adjustments */
.dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

/* Custom navbar styles */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-blue);
    border-bottom: 1px solid var(--border-light);
}

.navbar-brand {
    font-weight: 700;
    color: white !important;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Card styles */
.card {
    background: white;
    border: 1px solid var(--border-light);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    margin-bottom: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-blue);
}

.card-header {
    background: var(--gradient-primary);
    border-bottom: none;
    padding: 1rem;
    color: white;
    font-weight: 600;
    border-radius: 15px 15px 0 0;
}

/* Form styles */
.form-control {
    background: var(--bg-light);
    border: 2px solid var(--border-light);
    border-radius: 8px;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.form-control:focus {
    background: white;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
    color: var(--text-dark);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Button styles */
.btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-blue);
    filter: brightness(1.1);
}

.btn-success {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #4ade80 100%);
    border: none;
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #4ade80 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(34, 197, 94, 0.4);
    filter: brightness(1.1);
}

.btn-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 50%, #7dd3fc 100%);
    border: none;
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 50%, #7dd3fc 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(56, 189, 248, 0.4);
    filter: brightness(1.1);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fde047 100%);
    border: none;
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fde047 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(251, 191, 36, 0.4);
    filter: brightness(1.1);
}

/* Table styles */
.table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    color: var(--text-dark);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-light);
}

.table thead th {
    background: var(--gradient-primary);
    border-bottom: none;
    color: white;
    font-weight: 600;
}

.table tbody tr {
    border-bottom: 1px solid var(--border-light);
    transition: background 0.3s ease;
}

.table tbody tr:hover {
    background: var(--bg-light);
}

/* Dashboard widgets */
.dashboard-widget {
    background: white;
    border: 1px solid var(--border-light);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.dashboard-widget:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-blue);
}

.dashboard-widget .icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
    text-shadow: none;
}

.dashboard-widget .title {
    font-size: 1rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.dashboard-widget .value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-dark);
    text-shadow: none;
}

/* Alert styles */
.alert {
    border-radius: 12px;
    border: 1px solid var(--border-light);
    background: white;
    color: var(--text-dark);
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-color: #f87171;
    color: #dc2626;
}

.alert-success {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border-color: #4ade80;
    color: #16a34a;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    color: #d97706;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: var(--accent-color);
    color: var(--accent-dark);
}

/* Dropdown styles */
.dropdown-menu {
    background: white;
    border: 1px solid var(--border-light);
    border-radius: 10px;
    box-shadow: var(--shadow-light);
}

.dropdown-item {
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--bg-light);
    color: var(--accent-color);
}

/* Footer styles */
.footer {
    background: var(--gradient-primary);
    border-top: 1px solid var(--border-light);
    color: white;
}

/* Login page specific styles */
.login-container {
    min-height: 100vh;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.2) 0%, transparent 50%);
    z-index: 1;
}

.login-container > .container {
    position: relative;
    z-index: 2;
}

/* Login Card Styles */
.login-container .card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow-blue);
    overflow: hidden;
    transition: all 0.3s ease;
}

.login-container .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.3);
}

.login-container .card-header {
    background: var(--gradient-primary);
    border: none;
    padding: 2rem 1.5rem;
    position: relative;
}

.login-container .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Logo Styles */
.login-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    position: relative;
    overflow: hidden;
}

.login-logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.login-logo i,
.login-logo img {
    font-size: 2.5rem;
    color: white;
    position: relative;
    z-index: 1;
    width: 60px;
    height: 60px;
}

.login-title {
    color: white !important;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 400;
    margin-bottom: 1rem;
}

.login-form-title {
    color: white !important;
    font-weight: 600;
    margin-top: 1rem;
}

/* Login Form Styles */
.login-container .card-body {
    padding: 2rem;
    background: white;
}

.login-container .form-group {
    margin-bottom: 1.5rem;
}

.login-container .form-control {
    border: 2px solid var(--border-light);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-light);
    color: var(--text-dark);
}

.login-container .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    background: white;
}

.login-container label {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.login-container .btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-container .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.login-container .btn-primary:active {
    transform: translateY(0);
}

.login-container .alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border: 1px solid #f87171;
    color: #dc2626;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Divider Style */
.login-container hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    margin: 1rem 0;
}

/* Badge styles */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

.bg-warning {
    background: linear-gradient(45deg, #3d3d20 0%, #5a5a2d 100%) !important;
    color: #ffffcc !important;
}

.bg-success {
    background: linear-gradient(45deg, #203d20 0%, #2d5a2d 100%) !important;
    color: #ccffcc !important;
}

.bg-danger {
    background: linear-gradient(45deg, #3d2020 0%, #5a2d2d 100%) !important;
    color: #ffcccc !important;
}

/* Link styles */
a {
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .container {
        width: 100%;
        max-width: none;
    }

    .card {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }

    .dashboard-widget {
        padding: 1rem;
    }

    .dashboard-widget .icon {
        font-size: 2rem;
    }

    .dashboard-widget .value {
        font-size: 1.5rem;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-color);
}

/* Animation for smooth transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}