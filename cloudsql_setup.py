#!/usr/bin/env python3
"""
Cloud SQL setup script for Al-Ishrafi Accounting System
This script helps set up the database connection for Google Cloud SQL
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_database():
    """Setup database for production"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
    django.setup()
    
    # Run migrations
    print("Running database migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Collect static files
    print("Collecting static files...")
    execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
    
    # Create superuser if needed
    print("Creating admin user...")
    execute_from_command_line(['manage.py', 'create_admin'])
    
    print("Database setup completed!")

if __name__ == '__main__':
    setup_database()
