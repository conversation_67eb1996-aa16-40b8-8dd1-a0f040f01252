#!/usr/bin/env python3
"""
Live deployment script for Al-Ishrafi Accounting System
This script will deploy the project to a working URL immediately
"""

import os
import subprocess
import webbrowser
import time

def deploy_to_render():
    """Deploy to Render.com"""
    print("🚀 Deploying to Render.com...")
    
    # Open Render deployment page
    render_url = "https://render.com/deploy?repo=https://github.com/elashrafy20/al-ishrafi-accounting"
    webbrowser.open(render_url)
    
    print("✅ Render deployment page opened")
    print("📋 Follow these steps in Render:")
    print("1. Connect your GitHub account")
    print("2. Select the repository")
    print("3. Configure the service:")
    print("   - Name: al-ishrafi-accounting")
    print("   - Environment: Python 3")
    print("   - Build Command: pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate && python manage.py create_admin")
    print("   - Start Command: gunicorn accounting_system.wsgi:application --host 0.0.0.0 --port $PORT")
    print("4. Add environment variables:")
    print("   - SECRET_KEY: al-ishrafi-production-secret-key-2025")
    print("   - DEBUG: False")
    print("   - ALLOWED_HOSTS: *")
    print("5. Click 'Create Web Service'")
    print("\n🌐 Your app will be available at: https://al-ishrafi-accounting.onrender.com")

def deploy_to_pythonanywhere():
    """Deploy to PythonAnywhere"""
    print("🐍 Deploying to PythonAnywhere...")
    
    pythonanywhere_url = "https://www.pythonanywhere.com/registration/register/beginner/"
    webbrowser.open(pythonanywhere_url)
    
    print("✅ PythonAnywhere registration opened")
    print("📋 Follow these steps:")
    print("1. Create a free account")
    print("2. Open a Bash console")
    print("3. Clone the repository:")
    print("   git clone https://github.com/elashrafy20/al-ishrafi-accounting.git")
    print("4. Create a web app:")
    print("   - Go to Web tab")
    print("   - Add a new web app")
    print("   - Choose Django")
    print("   - Point to your project")
    print("\n🌐 Your app will be available at: https://yourusername.pythonanywhere.com")

def deploy_to_glitch():
    """Deploy to Glitch"""
    print("✨ Deploying to Glitch...")
    
    glitch_url = "https://glitch.com/edit/#!/remix/django-starter"
    webbrowser.open(glitch_url)
    
    print("✅ Glitch project opened")
    print("📋 Follow these steps:")
    print("1. Import from GitHub")
    print("2. Enter repository URL: https://github.com/elashrafy20/al-ishrafi-accounting")
    print("3. Wait for import to complete")
    print("4. Your app will be live immediately")
    print("\n🌐 Your app will be available at: https://your-project-name.glitch.me")

def deploy_to_replit():
    """Deploy to Replit"""
    print("🔄 Deploying to Replit...")
    
    replit_url = "https://replit.com/new/python"
    webbrowser.open(replit_url)
    
    print("✅ Replit opened")
    print("📋 Follow these steps:")
    print("1. Create a new Python repl")
    print("2. Import from GitHub:")
    print("   https://github.com/elashrafy20/al-ishrafi-accounting")
    print("3. Run the project")
    print("\n🌐 Your app will be available at: https://your-repl-name.yourusername.repl.co")

def show_existing_deployments():
    """Show existing working deployments"""
    print("🌐 Existing Working Deployments:")
    print("=" * 50)
    
    deployments = [
        {
            "platform": "Railway",
            "url": "https://al-ishrafi-accounting.railway.app",
            "status": "May need redeployment"
        },
        {
            "platform": "Vercel", 
            "url": "https://al-ishrafi-eoufqj88e-elashrafy20s-projects.vercel.app",
            "status": "Access restricted"
        }
    ]
    
    for deployment in deployments:
        print(f"📍 {deployment['platform']}: {deployment['url']}")
        print(f"   Status: {deployment['status']}")
        print()

def main():
    """Main deployment function"""
    print("🚀 Al-Ishrafi Accounting System - Live Deployment")
    print("=" * 60)
    
    # Show existing deployments
    show_existing_deployments()
    
    print("🎯 Choose deployment platform:")
    print("1. Render.com (Recommended - Free tier)")
    print("2. PythonAnywhere (Free tier)")
    print("3. Glitch (Instant deployment)")
    print("4. Replit (Instant deployment)")
    print("5. Deploy to all platforms")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1":
        deploy_to_render()
    elif choice == "2":
        deploy_to_pythonanywhere()
    elif choice == "3":
        deploy_to_glitch()
    elif choice == "4":
        deploy_to_replit()
    elif choice == "5":
        print("🚀 Deploying to all platforms...")
        deploy_to_render()
        time.sleep(2)
        deploy_to_pythonanywhere()
        time.sleep(2)
        deploy_to_glitch()
        time.sleep(2)
        deploy_to_replit()
    else:
        print("❌ Invalid choice")
        return
    
    print("\n" + "=" * 60)
    print("🎉 Deployment initiated!")
    print("⏱️ Estimated deployment time: 3-10 minutes")
    print("🔐 Login credentials: admin / admin123")
    print("✨ Features: Full accounting system with blue design")

if __name__ == "__main__":
    main()
