name: Deploy to Google App Engine

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Collect static files
      run: |
        python manage.py collectstatic --noinput
        
    - name: Run migrations
      run: |
        python manage.py migrate
        
    - name: Create admin user
      run: |
        python manage.py create_admin
        
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      
    - name: Deploy to App Engine
      run: |
        gcloud app deploy app.yaml --quiet --project=${{ secrets.GCP_PROJECT_ID }}
        
    - name: Get deployment URL
      run: |
        echo "Deployment successful!"
        echo "App URL: https://${{ secrets.GCP_PROJECT_ID }}.appspot.com"
