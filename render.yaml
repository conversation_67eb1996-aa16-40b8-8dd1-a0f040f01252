
services:
  - type: web
    name: al-ishrafi-accounting
    env: python
    buildCommand: "pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate"
    startCommand: "gunicorn accounting_system.wsgi:application"
    envVars:
      - key: SECRET_KEY
        value: "al-ishrafi-production-secret-key-2025"
      - key: DEBUG
        value: "False"
      - key: ALLOWED_HOSTS
        value: "*"
      - key: DATABASE_URL
        generateValue: true
    plan: free
