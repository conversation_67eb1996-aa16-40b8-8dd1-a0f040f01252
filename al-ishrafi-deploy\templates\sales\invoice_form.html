{% extends 'sales/base_sales.html' %}
{% load crispy_forms_tags %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'sales:invoice_list' %}">فواتير المبيعات</a></li>
<li class="breadcrumb-item active">فاتورة جديدة</li>
{% endblock %}

{% block sales_content %}
<form method="post" id="invoice_form">
    {% csrf_token %}
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">بيانات الفاتورة</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="number">رقم الفاتورة</label>
                        <input type="text" class="form-control" id="number" name="number" required>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="date">التاريخ</label>
                        <input type="date" class="form-control" id="date" name="date" required>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="customer">العميل</label>
                        <select class="form-control" id="customer" name="customer" required>
                            <option value="">اختر العميل</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="due_date">تاريخ الاستحقاق</label>
                        <input type="date" class="form-control" id="due_date" name="due_date" required>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">الأصناف</h5>
                <button type="button" class="btn btn-sm btn-success" id="add_line">
                    <i class="fas fa-plus"></i> إضافة صنف
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الضريبة %</th>
                            <th>الخصم</th>
                            <th>الإجمالي</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="invoice_lines">
                        <tr class="invoice-line">
                            <td>
                                <select class="form-control product-select" name="lines[0][product]" required>
                                    <option value="">اختر الصنف</option>
                                    {% for product in products %}
                                    <option value="{{ product.id }}" data-stock="{{ product.current_stock }}" data-price="{{ product.sale_price }}">
                                        {{ product.name }} (المخزون: {{ product.current_stock }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <input type="number" class="form-control quantity" name="lines[0][quantity]" min="0.01" step="0.01" required>
                            </td>
                            <td>
                                <input type="number" class="form-control price" name="lines[0][unit_price]" min="0.01" step="0.01" required>
                            </td>
                            <td>
                                <input type="number" class="form-control tax-rate" name="lines[0][tax_rate]" min="0" step="0.01" value="0">
                            </td>
                            <td>
                                <input type="number" class="form-control discount" name="lines[0][discount_amount]" min="0" step="0.01" value="0">
                            </td>
                            <td>
                                <span class="line-total">0.00</span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-line">
                                    <i class="fas fa-times"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <div class="row justify-content-end">
                <div class="col-md-4">
                    <table class="table table-sm">
                        <tr>
                            <th>الإجمالي قبل الضريبة:</th>
                            <td><span id="subtotal">0.00</span> ج.م</td>
                        </tr>
                        <tr>
                            <th>قيمة الضريبة:</th>
                            <td><span id="tax_amount_display">0.00</span> ج.م</td>
                        </tr>
                        <tr>
                            <th>الخصم:</th>
                            <td>
                                <input type="number" class="form-control form-control-sm" id="discount_amount" name="discount_amount" value="0" min="0" step="0.01">
                            </td>
                        </tr>
                        <tr class="table-primary">
                            <th>إجمالي الفاتورة:</th>
                            <td><span id="total_amount">0.00</span> ج.م</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <div class="form-group">
                <label for="notes">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>
        </div>
    </div>

    <div class="text-center">
        <button type="submit" class="btn btn-primary" name="status" value="draft">
            <i class="fas fa-save me-2"></i>
            حفظ كمسودة
        </button>
        <button type="submit" class="btn btn-success" name="status" value="posted">
            <i class="fas fa-check me-2"></i>
            ترحيل الفاتورة
        </button>
        <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>
            إلغاء
        </a>
    </div>
</form>
{% endblock %}