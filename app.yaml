runtime: python39

# Environment variables
env_variables:
  SECRET_KEY: "al-ishrafi-production-secret-key-2025"
  DEBUG: "False"
  ALLOWED_HOSTS: "*"
  DATABASE_URL: "sqlite:///db.sqlite3"
  DJANGO_SETTINGS_MODULE: "accounting_system.settings"

# Static file handlers
handlers:
- url: /static
  static_dir: staticfiles/
  secure: always
  expiration: 1d

- url: /media
  static_dir: media/
  secure: always
  expiration: 1d

- url: /favicon\.ico
  static_files: staticfiles/images/favicon.svg
  upload: staticfiles/images/favicon\.svg
  secure: always

- url: /.*
  script: auto
  secure: always

# Automatic scaling configuration
automatic_scaling:
  min_instances: 0
  max_instances: 10
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6
  max_concurrent_requests: 80

# Resource settings
resources:
  cpu: 1
  memory_gb: 0.5
  disk_size_gb: 10

# Health check
readiness_check:
  path: "/"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: "/"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
