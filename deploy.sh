#!/bin/bash

echo "🚀 Deploying Al-Ishrafi Accounting System to Firebase..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK is not installed. Please install it first:"
    echo "https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Run migrations (if using Cloud SQL)
echo "🗄️ Running database migrations..."
python manage.py migrate

# Create admin user
echo "👤 Creating admin user..."
python manage.py create_admin

# Deploy to App Engine
echo "🌐 Deploying to Google App Engine..."
gcloud app deploy app.yaml --quiet

echo "✅ Deployment completed!"
echo "🌍 Your app should be available at: https://YOUR_PROJECT_ID.appspot.com"
echo ""
echo "📝 Next steps:"
echo "1. Set up Cloud SQL database if needed"
echo "2. Configure custom domain if desired"
echo "3. Set up SSL certificate"
echo "4. Configure environment variables in app.yaml"
