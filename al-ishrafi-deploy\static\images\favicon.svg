<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)"/>
  
  <!-- Calculator Icon -->
  <g transform="translate(8, 6)">
    <!-- Calculator Body -->
    <rect x="2" y="2" width="12" height="16" rx="1" ry="1" fill="white" opacity="0.9"/>
    
    <!-- Screen -->
    <rect x="3" y="3" width="10" height="4" rx="0.5" ry="0.5" fill="#1e3a8a"/>
    
    <!-- Screen Text -->
    <text x="8" y="6" text-anchor="middle" font-family="Arial, sans-serif" font-size="2.5" fill="white" font-weight="bold">123</text>
    
    <!-- Buttons -->
    <circle cx="5" cy="10" r="1" fill="#3b82f6"/>
    <circle cx="8" cy="10" r="1" fill="#3b82f6"/>
    <circle cx="11" cy="10" r="1" fill="#3b82f6"/>
    
    <circle cx="5" cy="13" r="1" fill="#3b82f6"/>
    <circle cx="8" cy="13" r="1" fill="#3b82f6"/>
    <circle cx="11" cy="13" r="1" fill="#60a5fa"/>
    
    <circle cx="5" cy="16" r="1" fill="#3b82f6"/>
    <circle cx="8" cy="16" r="1" fill="#3b82f6"/>
    <circle cx="11" cy="16" r="1" fill="#60a5fa"/>
  </g>
</svg>
