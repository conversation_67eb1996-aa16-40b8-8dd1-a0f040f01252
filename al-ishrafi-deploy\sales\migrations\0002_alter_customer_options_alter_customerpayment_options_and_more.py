# Generated by Django 5.1.6 on 2025-03-02 22:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_account_options_alter_accounttype_options_and_more'),
        ('inventory', '0002_alter_category_options_alter_product_options_and_more'),
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='customer',
            options={'ordering': ['code'], 'verbose_name': 'عميل', 'verbose_name_plural': 'العملاء'},
        ),
        migrations.AlterModelOptions(
            name='customerpayment',
            options={'ordering': ['-date', '-number'], 'verbose_name': 'سند قبض', 'verbose_name_plural': 'سندات القبض'},
        ),
        migrations.AlterModelOptions(
            name='salesinvoice',
            options={'ordering': ['-date', '-number'], 'verbose_name': 'فاتورة مبيعات', 'verbose_name_plural': 'فواتير المبيعات'},
        ),
        migrations.AlterModelOptions(
            name='salesinvoiceline',
            options={'verbose_name': 'بند الفاتورة', 'verbose_name_plural': 'بنود الفاتورة'},
        ),
        migrations.AlterModelOptions(
            name='salesreturn',
            options={'ordering': ['-date', '-number'], 'verbose_name': 'مرتجع مبيعات', 'verbose_name_plural': 'مرتجعات المبيعات'},
        ),
        migrations.AlterModelOptions(
            name='salesreturnline',
            options={'verbose_name': 'بند المرتجع', 'verbose_name_plural': 'بنود المرتجع'},
        ),
        migrations.AlterField(
            model_name='customer',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customers', to='accounts.account', verbose_name='الحساب'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='address',
            field=models.TextField(verbose_name='العنوان'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='كود العميل'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='contact_person',
            field=models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='credit_limit',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='حد الائتمان'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='current_balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='discount_percentage',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='email',
            field=models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='mobile',
            field=models.CharField(blank=True, max_length=20, verbose_name='رقم الجوال'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='name',
            field=models.CharField(max_length=200, verbose_name='اسم العميل'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='phone',
            field=models.CharField(max_length=20, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='tax_number',
            field=models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customer_payments', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='customer_payments', to='sales.customer', verbose_name='العميل'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='date',
            field=models.DateField(verbose_name='تاريخ السند'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم السند'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='payment_method',
            field=models.CharField(choices=[('cash', 'نقدي'), ('bank', 'تحويل بنكي'), ('cheque', 'شيك'), ('card', 'بطاقة ائتمان')], max_length=10, verbose_name='طريقة الدفع'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='reference',
            field=models.CharField(blank=True, max_length=50, verbose_name='المرجع'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحل'), ('cancelled', 'ملغي')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='customerpayment',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_invoices', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_invoices', to='sales.customer', verbose_name='العميل'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='date',
            field=models.DateField(verbose_name='تاريخ الفاتورة'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='due_date',
            field=models.DateField(verbose_name='تاريخ الاستحقاق'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحلة'), ('cancelled', 'ملغاة')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المجموع'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الضريبة'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الإجمالي'),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.salesinvoice', verbose_name='الفاتورة'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sale_lines', to='inventory.product', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='tax_rate',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الضريبة'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='total',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الإجمالي'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceline',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_returns', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_returns', to='sales.customer', verbose_name='العميل'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='date',
            field=models.DateField(verbose_name='تاريخ المرتجع'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='returns', to='sales.salesinvoice', verbose_name='فاتورة المبيعات'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم المرتجع'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='reason',
            field=models.TextField(verbose_name='سبب المرتجع'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحل'), ('cancelled', 'ملغي')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='إجمالي المرتجع'),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='salesreturnline',
            name='invoice_line',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='return_lines', to='sales.salesinvoiceline', verbose_name='بند الفاتورة'),
        ),
        migrations.AlterField(
            model_name='salesreturnline',
            name='quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية'),
        ),
        migrations.AlterField(
            model_name='salesreturnline',
            name='sales_return',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.salesreturn', verbose_name='مرتجع المبيعات'),
        ),
        migrations.AlterField(
            model_name='salesreturnline',
            name='total',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الإجمالي'),
        ),
        migrations.AlterField(
            model_name='salesreturnline',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة'),
        ),
    ]
