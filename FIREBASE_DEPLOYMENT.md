# نشر نظام الإشرافي للمحاسبة على Firebase (Google App Engine)

## المتطلبات المسبقة

1. **حساب Google Cloud Platform**
   - إنشاء مشروع جديد في [Google Cloud Console](https://console.cloud.google.com/)
   - تفعيل App Engine API
   - تفعيل Cloud SQL API (إذا كنت تريد قاعدة بيانات PostgreSQL)

2. **تثبيت Google Cloud SDK**
   ```bash
   # Windows
   # تحميل من: https://cloud.google.com/sdk/docs/install
   
   # macOS
   brew install google-cloud-sdk
   
   # Linux
   curl https://sdk.cloud.google.com | bash
   ```

3. **تسجيل الدخول وإعداد المشروع**
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   gcloud app create --region=us-central1
   ```

## خطوات النشر

### 1. إعداد قاعدة البيانات (اختياري - Cloud SQL)

```bash
# إنشاء instance لـ PostgreSQL
gcloud sql instances create al-ishrafi-db \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# إنشاء قاعدة البيانات
gcloud sql databases create accounting --instance=al-ishrafi-db

# إنشاء مستخدم
gcloud sql users create dbuser --instance=al-ishrafi-db --password=YOUR_PASSWORD
```

### 2. تحديث app.yaml

```yaml
runtime: python39

env_variables:
  SECRET_KEY: "your-secret-key-here"
  DEBUG: "False"
  ALLOWED_HOSTS: "*"
  # للاستخدام مع Cloud SQL
  DATABASE_URL: "postgres://dbuser:YOUR_PASSWORD@//cloudsql/YOUR_PROJECT_ID:us-central1:al-ishrafi-db/accounting"

handlers:
- url: /static
  static_dir: staticfiles/
  secure: always

- url: /.*
  script: auto
  secure: always

automatic_scaling:
  min_instances: 0
  max_instances: 10
  target_cpu_utilization: 0.6
```

### 3. النشر

```bash
# جمع الملفات الثابتة
python manage.py collectstatic --noinput

# تشغيل الهجرات
python manage.py migrate

# إنشاء مستخدم إداري
python manage.py create_admin

# النشر
gcloud app deploy app.yaml --quiet
```

### 4. أو استخدام سكريبت النشر التلقائي

```bash
chmod +x deploy.sh
./deploy.sh
```

## إعدادات إضافية

### 1. ربط النطاق المخصص

```bash
gcloud app domain-mappings create YOUR_DOMAIN.com
```

### 2. إعداد HTTPS

```bash
gcloud app ssl-certificates create --domains=YOUR_DOMAIN.com
```

### 3. مراقبة التطبيق

- [Google Cloud Console](https://console.cloud.google.com/)
- [App Engine Dashboard](https://console.cloud.google.com/appengine)
- [Cloud SQL Dashboard](https://console.cloud.google.com/sql)

## استكشاف الأخطاء

### 1. عرض السجلات

```bash
gcloud app logs tail -s default
```

### 2. الاتصال بقاعدة البيانات

```bash
gcloud sql connect al-ishrafi-db --user=dbuser
```

### 3. تشغيل الأوامر على الخادم

```bash
gcloud app deploy --version=debug
gcloud app browse
```

## التكلفة

- **App Engine**: مجاني حتى حد معين، ثم حسب الاستخدام
- **Cloud SQL**: حوالي $7-15 شهرياً للـ micro instance
- **التخزين**: مجاني حتى 5GB

## الأمان

- تم تفعيل HTTPS تلقائياً
- إعدادات الأمان محسنة للإنتاج
- حماية CSRF و XSS مفعلة

## الدعم

للمساعدة أو الاستفسارات:
- [Google Cloud Support](https://cloud.google.com/support)
- [App Engine Documentation](https://cloud.google.com/appengine/docs)
