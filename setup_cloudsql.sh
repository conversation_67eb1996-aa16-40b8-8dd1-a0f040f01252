#!/bin/bash

echo "🗄️ Setting up Cloud SQL for Al-Ishrafi Accounting System..."

# Variables
PROJECT_ID=$(gcloud config get-value project)
INSTANCE_NAME="al-ishrafi-db"
DATABASE_NAME="accounting"
DB_USER="dbuser"
REGION="us-central1"

echo "📋 Project ID: $PROJECT_ID"
echo "📋 Instance Name: $INSTANCE_NAME"
echo "📋 Database Name: $DATABASE_NAME"
echo "📋 Region: $REGION"

# Check if gcloud is configured
if [ -z "$PROJECT_ID" ]; then
    echo "❌ No project configured. Please run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable sqladmin.googleapis.com
gcloud services enable sql-component.googleapis.com

# Create Cloud SQL instance
echo "🏗️ Creating Cloud SQL instance..."
gcloud sql instances create $INSTANCE_NAME \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=$REGION \
    --storage-type=SSD \
    --storage-size=10GB \
    --backup-start-time=03:00 \
    --enable-bin-log \
    --maintenance-window-day=SUN \
    --maintenance-window-hour=04 \
    --maintenance-release-channel=production

# Wait for instance to be ready
echo "⏳ Waiting for instance to be ready..."
gcloud sql instances describe $INSTANCE_NAME --format="value(state)" | grep -q "RUNNABLE"
while [ $? -ne 0 ]; do
    echo "⏳ Still waiting..."
    sleep 10
    gcloud sql instances describe $INSTANCE_NAME --format="value(state)" | grep -q "RUNNABLE"
done

echo "✅ Instance is ready!"

# Create database
echo "📊 Creating database..."
gcloud sql databases create $DATABASE_NAME --instance=$INSTANCE_NAME

# Create database user
echo "👤 Creating database user..."
echo "Please enter a password for the database user:"
read -s DB_PASSWORD
gcloud sql users create $DB_USER --instance=$INSTANCE_NAME --password=$DB_PASSWORD

# Get connection name
CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME --format="value(connectionName)")

echo "✅ Cloud SQL setup completed!"
echo ""
echo "📝 Database connection details:"
echo "Instance: $INSTANCE_NAME"
echo "Database: $DATABASE_NAME"
echo "User: $DB_USER"
echo "Connection Name: $CONNECTION_NAME"
echo ""
echo "🔧 Update your app.yaml with:"
echo "DATABASE_URL: \"postgres://$DB_USER:$DB_PASSWORD@//cloudsql/$CONNECTION_NAME/$DATABASE_NAME\""
echo ""
echo "📋 Next steps:"
echo "1. Update app.yaml with the DATABASE_URL above"
echo "2. Add cloud_sql_instances to app.yaml:"
echo "   cloud_sql_instances:"
echo "     - $CONNECTION_NAME"
echo "3. Deploy your application: gcloud app deploy"
