
# 🚀 Al-Ishrafi Accounting System - Deployment Package

## Quick Deploy Options:

### Option 1: Google Cloud Shell (Easiest)
1. Go to: https://console.cloud.google.com/
2. Open Cloud Shell (terminal icon in top bar)
3. Upload this entire folder to Cloud Shell
4. Run these commands:
   ```bash
   cd al-ishrafi-deploy
   gcloud app deploy app.yaml --quiet
   ```

### Option 2: Local Google Cloud SDK
1. Install Google Cloud SDK: https://cloud.google.com/sdk/docs/install
2. Run these commands:
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   gcloud app create --region=us-central1
   gcloud app deploy app.yaml --quiet
   ```

### Option 3: GitHub Actions
1. Push this code to GitHub
2. Set up GitHub Actions with the included workflow
3. Add secrets: GCP_SA_KEY and GCP_PROJECT_ID

## Files Included:
- ✅ Django application code
- ✅ Static files and templates
- ✅ App Engine configuration (app.yaml)
- ✅ Cloud SQL configuration (app-cloudsql.yaml)
- ✅ Requirements and dependencies
- ✅ Firebase configuration
- ✅ GitHub Actions workflow

## After Deployment:
- Access your app at: https://YOUR_PROJECT_ID.appspot.com
- Login with: admin / admin123
- Change the admin password immediately!

## Support:
- Google Cloud Docs: https://cloud.google.com/appengine/docs
- Firebase Docs: https://firebase.google.com/docs

Generated on: 2025-07-09 18:43:10
