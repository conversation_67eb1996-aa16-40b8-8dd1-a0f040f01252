@echo off
echo 🚀 Deploying Al-Ishrafi Accounting System to Firebase...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Google Cloud SDK is not installed. Please install it first:
    echo https://cloud.google.com/sdk/docs/install
    pause
    exit /b 1
)

REM Collect static files
echo 📁 Collecting static files...
python manage.py collectstatic --noinput

REM Run migrations
echo 🗄️ Running database migrations...
python manage.py migrate

REM Create admin user
echo 👤 Creating admin user...
python manage.py create_admin

REM Deploy to App Engine
echo 🌐 Deploying to Google App Engine...
gcloud app deploy app.yaml --quiet

echo ✅ Deployment completed!
echo 🌍 Your app should be available at: https://YOUR_PROJECT_ID.appspot.com
echo.
echo 📝 Next steps:
echo 1. Set up Cloud SQL database if needed
echo 2. Configure custom domain if desired
echo 3. Set up SSL certificate
echo 4. Configure environment variables in app.yaml

pause
