apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: al-ishrafi-accounting
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/max-scale: "10"
        run.googleapis.com/min-scale: "0"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/al-ishrafi-accounting
        ports:
        - containerPort: 8080
        env:
        - name: SECRET_KEY
          value: "al-ishrafi-production-secret-key-2025"
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "*"
        - name: DATABASE_URL
          value: "sqlite:///db.sqlite3"
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
  traffic:
  - percent: 100
    latestRevision: true
