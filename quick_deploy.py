#!/usr/bin/env python3
"""
Quick deployment script for Al-Ishrafi Accounting System to Firebase
"""

import os
import subprocess
import sys

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error in {description}: {e.stderr}")
        return None

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if gcloud is installed
    if run_command("gcloud --version", "Checking Google Cloud SDK") is None:
        print("❌ Google Cloud SDK is not installed.")
        print("Please install it from: https://cloud.google.com/sdk/docs/install")
        return False
    
    # Check if user is authenticated
    if run_command("gcloud auth list --filter=status:ACTIVE", "Checking authentication") is None:
        print("❌ Not authenticated with Google Cloud.")
        print("Please run: gcloud auth login")
        return False
    
    return True

def prepare_for_deployment():
    """Prepare the application for deployment"""
    print("📦 Preparing application for deployment...")
    
    # Install dependencies
    run_command("pip install -r requirements.txt", "Installing dependencies")
    
    # Collect static files
    run_command("python manage.py collectstatic --noinput", "Collecting static files")
    
    # Run migrations
    run_command("python manage.py migrate", "Running database migrations")
    
    # Create admin user
    run_command("python manage.py create_admin", "Creating admin user")

def deploy_to_firebase():
    """Deploy to Firebase (Google App Engine)"""
    print("🚀 Deploying to Firebase...")
    
    # Deploy to App Engine
    result = run_command("gcloud app deploy app.yaml --quiet", "Deploying to App Engine")
    
    if result is not None:
        print("🎉 Deployment successful!")
        
        # Get the app URL
        url_result = run_command("gcloud app browse --no-launch-browser", "Getting app URL")
        if url_result:
            print(f"🌐 Your app is available at: {url_result.strip()}")
        
        print("\n📝 Next steps:")
        print("1. Visit your app URL to test it")
        print("2. Set up custom domain if needed")
        print("3. Configure Cloud SQL for production database")
        print("4. Set up monitoring and alerts")
        
        return True
    else:
        print("❌ Deployment failed!")
        return False

def main():
    """Main deployment function"""
    print("🚀 Al-Ishrafi Accounting System - Firebase Deployment")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Prepare for deployment
    prepare_for_deployment()
    
    # Deploy to Firebase
    if deploy_to_firebase():
        print("\n✅ Deployment completed successfully!")
    else:
        print("\n❌ Deployment failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
