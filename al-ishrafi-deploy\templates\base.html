{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشرافي | {% block title %}{% endblock %}</title>
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'images/favicon.svg' %}">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Blue Theme CSS -->
    <style>
        /* Blue and White Theme Variables */
        :root {
            --bg-primary: #0f1419;
            --bg-secondary: #1e3a8a;
            --bg-tertiary: #3b82f6;
            --bg-light: #f8fafc;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;
            --text-dark: #1e293b;
            --accent-color: #3b82f6;
            --accent-light: #60a5fa;
            --accent-dark: #1d4ed8;
            --border-color: #334155;
            --border-light: #e2e8f0;
            --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.3);
            --shadow-light: 0 2px 10px rgba(59, 130, 246, 0.1);
            --shadow-blue: 0 4px 20px rgba(59, 130, 246, 0.2);
            --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
            --gradient-secondary: linear-gradient(135deg, #0f1419 0%, #1e3a8a 100%);
        }

        /* General styles */
        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gradient-secondary);
            color: var(--text-primary);
            min-height: 100vh;
        }

        /* Navbar styles */
        .navbar {
            background: var(--gradient-primary) !important;
            box-shadow: var(--shadow-blue);
            border-bottom: 1px solid var(--border-light);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            font-size: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        /* Card styles */
        .card {
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 15px;
            box-shadow: var(--shadow-light);
            margin-bottom: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-blue);
        }

        .card-header {
            background: var(--gradient-primary);
            border-bottom: none;
            padding: 1rem;
            color: white;
            font-weight: 600;
            border-radius: 15px 15px 0 0;
        }

        /* Dashboard widgets */
        .dashboard-widget {
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
        }

        .dashboard-widget:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-blue);
        }

        .dashboard-widget .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--accent-color);
            text-shadow: none;
        }

        .dashboard-widget .title {
            font-size: 1rem;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .dashboard-widget .value {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--text-dark);
            text-shadow: none;
        }

        /* Button styles */
        .btn {
            border-radius: 8px;
            padding: 0.6rem 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            background: var(--gradient-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-blue);
            filter: brightness(1.1);
        }

        .btn-success {
            background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #4ade80 100%);
            border: none;
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #4ade80 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(34, 197, 94, 0.4);
            filter: brightness(1.1);
        }

        .btn-info {
            background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 50%, #7dd3fc 100%);
            border: none;
            color: white;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 50%, #7dd3fc 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(56, 189, 248, 0.4);
            filter: brightness(1.1);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fde047 100%);
            border: none;
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fde047 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(251, 191, 36, 0.4);
            filter: brightness(1.1);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
            border: none;
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.4);
            filter: brightness(1.1);
        }

        /* Footer styles */
        .footer {
            background: var(--gradient-primary);
            border-top: 1px solid var(--border-light);
            color: white;
        }

        /* Dropdown styles */
        .dropdown-menu {
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 10px;
            box-shadow: var(--shadow-light);
        }

        .dropdown-item {
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: var(--bg-light);
            color: var(--accent-color);
        }

        /* Form styles */
        .form-control {
            background: var(--bg-light);
            border: 2px solid var(--border-light);
            border-radius: 8px;
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: white;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
            color: var(--text-dark);
        }

        /* Table styles */
        .table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            color: var(--text-dark);
            border: 1px solid var(--border-light);
            box-shadow: var(--shadow-light);
        }

        .table thead th {
            background: var(--gradient-primary);
            border-bottom: none;
            color: white;
            font-weight: 600;
        }

        .table tbody tr {
            border-bottom: 1px solid var(--border-light);
            transition: background 0.3s ease;
        }

        .table tbody tr:hover {
            background: var(--bg-light);
        }

        /* Alert styles */
        .alert {
            border-radius: 12px;
            border: 1px solid var(--border-light);
            background: white;
            color: var(--text-dark);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border-color: #f87171;
            color: #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border-color: #4ade80;
            color: #16a34a;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;
            color: #d97706;
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: var(--accent-color);
            color: var(--accent-dark);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="{% static 'images/logo.svg' %}" alt="الإشرافي" style="width: 32px; height: 32px; margin-left: 8px;">
                الإشرافي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-cog"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-shopping-cart"></i>
                            المبيعات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_create' %}">فاتورة جديدة</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_list' %}">قائمة الفواتير</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/sales/customer/">العملاء</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i>
                            المشتريات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'purchases:invoice_create' %}">فاتورة جديدة</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:invoice_list' %}">قائمة الفواتير</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/purchases/supplier/">الموردين</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i>
                            المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/inventory/product/">المنتجات</a></li>
                            <li><a class="dropdown-item" href="/admin/inventory/category/">التصنيفات</a></li>
                            <li><a class="dropdown-item" href="/admin/inventory/stockmovement/">حركة المخزون</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calculator"></i>
                            الحسابات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/accounts/account/">دليل الحسابات</a></li>
                            <li><a class="dropdown-item" href="/admin/accounts/journalentry/">القيود اليومية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/accounts/financialstatement/">التقارير المالية</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/admin/password_change/">تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">تسجيل الدخول</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <span>© 2025 الإشرافي. جميع الحقوق محفوظة</span>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="/static/js/main.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>