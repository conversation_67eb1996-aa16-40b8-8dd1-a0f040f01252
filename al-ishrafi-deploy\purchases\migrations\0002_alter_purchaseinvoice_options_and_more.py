# Generated by Django 5.1.6 on 2025-03-02 22:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_account_options_alter_accounttype_options_and_more'),
        ('inventory', '0002_alter_category_options_alter_product_options_and_more'),
        ('purchases', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='purchaseinvoice',
            options={'ordering': ['-date', '-number'], 'verbose_name': 'فاتورة مشتريات', 'verbose_name_plural': 'فواتير المشتريات'},
        ),
        migrations.AlterModelOptions(
            name='purchaseinvoiceline',
            options={'verbose_name': 'بند الفاتورة', 'verbose_name_plural': 'بنود الفاتورة'},
        ),
        migrations.AlterModelOptions(
            name='supplier',
            options={'ordering': ['code'], 'verbose_name': 'مورد', 'verbose_name_plural': 'الموردين'},
        ),
        migrations.AlterModelOptions(
            name='supplierpayment',
            options={'ordering': ['-date', '-number'], 'verbose_name': 'سند صرف', 'verbose_name_plural': 'سندات الصرف'},
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_invoices', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='date',
            field=models.DateField(verbose_name='تاريخ الفاتورة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='due_date',
            field=models.DateField(verbose_name='تاريخ الاستحقاق'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحلة'), ('cancelled', 'ملغاة')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المجموع'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_invoices', to='purchases.supplier', verbose_name='المورد'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الضريبة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الإجمالي'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='purchases.purchaseinvoice', verbose_name='الفاتورة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_lines', to='inventory.product', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='quantity',
            field=models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='tax_rate',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الضريبة'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='total',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الإجمالي'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceline',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='suppliers', to='accounts.account', verbose_name='الحساب'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='address',
            field=models.TextField(verbose_name='العنوان'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='كود المورد'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='contact_person',
            field=models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='credit_limit',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='حد الائتمان'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='current_balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='email',
            field=models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='mobile',
            field=models.CharField(blank=True, max_length=20, verbose_name='رقم الجوال'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='name',
            field=models.CharField(max_length=200, verbose_name='اسم المورد'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='phone',
            field=models.CharField(max_length=20, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='tax_number',
            field=models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='supplier_payments', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='date',
            field=models.DateField(verbose_name='تاريخ السند'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم السند'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='payment_method',
            field=models.CharField(choices=[('cash', 'نقدي'), ('bank', 'تحويل بنكي'), ('cheque', 'شيك')], max_length=10, verbose_name='طريقة الدفع'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='reference',
            field=models.CharField(blank=True, max_length=50, verbose_name='المرجع'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحل'), ('cancelled', 'ملغي')], default='draft', max_length=10, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='supplier_payments', to='purchases.supplier', verbose_name='المورد'),
        ),
        migrations.AlterField(
            model_name='supplierpayment',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
    ]
