#!/usr/bin/env python3
"""
Web-based deployment helper for Al-Ishrafi Accounting System
This script helps prepare the project for deployment without requiring local Google Cloud SDK
"""

import os
import sys
import zipfile
import shutil
from datetime import datetime

def create_deployment_package():
    """Create a deployment package ready for upload"""
    print("📦 Creating deployment package...")
    
    # Create deployment directory
    deploy_dir = "al-ishrafi-deploy"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # Files and directories to include
    include_items = [
        'accounting_system/',
        'accounts/',
        'inventory/',
        'sales/',
        'purchases/',
        'templates/',
        'static/',
        'staticfiles/',
        'manage.py',
        'main.py',
        'app.yaml',
        'app-cloudsql.yaml',
        'requirements.txt',
        'firebase.json',
        'package.json',
        '.gcloudignore'
    ]
    
    # Copy files to deployment directory
    for item in include_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(deploy_dir, item))
                print(f"✅ Copied directory: {item}")
            else:
                shutil.copy2(item, deploy_dir)
                print(f"✅ Copied file: {item}")
        else:
            print(f"⚠️ Item not found: {item}")
    
    # Create deployment instructions
    instructions = """
# 🚀 Al-Ishrafi Accounting System - Deployment Package

## Quick Deploy Options:

### Option 1: Google Cloud Shell (Easiest)
1. Go to: https://console.cloud.google.com/
2. Open Cloud Shell (terminal icon in top bar)
3. Upload this entire folder to Cloud Shell
4. Run these commands:
   ```bash
   cd al-ishrafi-deploy
   gcloud app deploy app.yaml --quiet
   ```

### Option 2: Local Google Cloud SDK
1. Install Google Cloud SDK: https://cloud.google.com/sdk/docs/install
2. Run these commands:
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   gcloud app create --region=us-central1
   gcloud app deploy app.yaml --quiet
   ```

### Option 3: GitHub Actions
1. Push this code to GitHub
2. Set up GitHub Actions with the included workflow
3. Add secrets: GCP_SA_KEY and GCP_PROJECT_ID

## Files Included:
- ✅ Django application code
- ✅ Static files and templates
- ✅ App Engine configuration (app.yaml)
- ✅ Cloud SQL configuration (app-cloudsql.yaml)
- ✅ Requirements and dependencies
- ✅ Firebase configuration
- ✅ GitHub Actions workflow

## After Deployment:
- Access your app at: https://YOUR_PROJECT_ID.appspot.com
- Login with: admin / admin123
- Change the admin password immediately!

## Support:
- Google Cloud Docs: https://cloud.google.com/appengine/docs
- Firebase Docs: https://firebase.google.com/docs

Generated on: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
"""
    
    with open(os.path.join(deploy_dir, "DEPLOYMENT_INSTRUCTIONS.txt"), "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print(f"✅ Created deployment instructions")
    
    # Create ZIP file
    zip_filename = f"al-ishrafi-deploy-{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    print(f"📦 Creating ZIP file: {zip_filename}")
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deploy_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, deploy_dir)
                zipf.write(file_path, arc_path)
                
    print(f"✅ Created deployment package: {zip_filename}")
    print(f"📁 Package size: {os.path.getsize(zip_filename) / 1024 / 1024:.2f} MB")
    
    return zip_filename, deploy_dir

def prepare_for_deployment():
    """Prepare the application for deployment"""
    print("🔧 Preparing application for deployment...")
    
    # Collect static files
    print("📁 Collecting static files...")
    os.system("python manage.py collectstatic --noinput")
    
    # Run migrations
    print("🗄️ Running database migrations...")
    os.system("python manage.py migrate")
    
    # Create admin user
    print("👤 Creating admin user...")
    os.system("python manage.py create_admin")
    
    print("✅ Application prepared successfully!")

def show_deployment_options():
    """Show available deployment options"""
    print("""
🚀 Al-Ishrafi Accounting System - Deployment Options

Choose your deployment method:

1. 📦 Create Deployment Package (Recommended)
   - Creates a ready-to-deploy package
   - Can be uploaded to Google Cloud Shell
   - Includes all necessary files and instructions

2. 🌐 Deploy via Web Console
   - Use Google Cloud Console
   - Upload files manually
   - Deploy through web interface

3. 🔄 GitHub Actions (Automated)
   - Push code to GitHub
   - Automatic deployment on every commit
   - Requires GitHub repository setup

4. 💻 Local Deployment
   - Requires Google Cloud SDK installation
   - Direct deployment from local machine
   - Full control over deployment process

Which option would you like to use? (1-4): """)

def main():
    """Main deployment helper function"""
    print("🚀 Al-Ishrafi Accounting System - Deployment Helper")
    print("=" * 60)
    
    # Prepare application
    prepare_for_deployment()
    
    # Create deployment package
    zip_file, deploy_dir = create_deployment_package()
    
    print("\n" + "=" * 60)
    print("🎉 Deployment package created successfully!")
    print(f"📦 Package: {zip_file}")
    print(f"📁 Directory: {deploy_dir}")
    
    print("\n📋 Next Steps:")
    print("1. Go to: https://console.cloud.google.com/")
    print("2. Create a new project or select existing one")
    print("3. Open Cloud Shell (terminal icon)")
    print("4. Upload the deployment package")
    print("5. Extract and deploy:")
    print("   unzip " + zip_file)
    print("   cd al-ishrafi-deploy")
    print("   gcloud app deploy app.yaml --quiet")
    
    print("\n🌐 Alternative: Use GitHub Actions")
    print("1. Push code to GitHub repository")
    print("2. Set up GitHub Actions secrets")
    print("3. Automatic deployment on every push")
    
    print("\n✅ Your Al-Ishrafi Accounting System is ready for deployment!")

if __name__ == "__main__":
    main()
