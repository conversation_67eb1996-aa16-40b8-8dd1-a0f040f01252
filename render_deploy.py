#!/usr/bin/env python3
"""
Quick deployment to Render.com for Al-Ishrafi Accounting System
"""

import os
import subprocess
import webbrowser

def create_render_files():
    """Create necessary files for Render deployment"""
    
    # Create render.yaml
    render_config = """
services:
  - type: web
    name: al-ishrafi-accounting
    env: python
    buildCommand: "pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate"
    startCommand: "gunicorn accounting_system.wsgi:application"
    envVars:
      - key: SECRET_KEY
        value: "al-ishrafi-production-secret-key-2025"
      - key: DEBUG
        value: "False"
      - key: ALLOWED_HOSTS
        value: "*"
      - key: DATABASE_URL
        generateValue: true
    plan: free
"""
    
    with open("render.yaml", "w") as f:
        f.write(render_config)
    
    print("✅ Created render.yaml")
    
    # Create build script
    build_script = """#!/bin/bash
pip install -r requirements.txt
python manage.py collectstatic --noinput
python manage.py migrate
python manage.py create_admin
"""
    
    with open("build.sh", "w") as f:
        f.write(build_script)
    
    print("✅ Created build.sh")
    
    # Create start script
    start_script = """#!/bin/bash
gunicorn accounting_system.wsgi:application --host 0.0.0.0 --port $PORT
"""
    
    with open("start.sh", "w") as f:
        f.write(start_script)
    
    print("✅ Created start.sh")

def setup_github_repo():
    """Setup GitHub repository for deployment"""
    print("🔧 Setting up GitHub repository...")
    
    commands = [
        "git init",
        "git add .",
        "git commit -m 'Al-Ishrafi Accounting System - Ready for deployment'",
    ]
    
    for cmd in commands:
        try:
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            print(f"✅ {cmd}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ {cmd} - {e.stderr}")

def open_deployment_services():
    """Open deployment services in browser"""
    services = [
        ("Render", "https://render.com"),
        ("Railway", "https://railway.app"),
        ("Vercel", "https://vercel.com"),
        ("Netlify", "https://netlify.com")
    ]
    
    print("🌐 Opening deployment services...")
    for name, url in services:
        try:
            webbrowser.open(url)
            print(f"✅ Opened {name}: {url}")
        except:
            print(f"⚠️ Could not open {name}: {url}")

def main():
    """Main deployment function"""
    print("🚀 Al-Ishrafi Accounting System - Quick Deployment")
    print("=" * 60)
    
    # Create deployment files
    create_render_files()
    
    # Setup git repository
    setup_github_repo()
    
    # Open deployment services
    open_deployment_services()
    
    print("\n" + "=" * 60)
    print("🎉 Deployment files created successfully!")
    
    print("\n📋 Next Steps:")
    print("1. Push code to GitHub:")
    print("   git remote add origin https://github.com/USERNAME/al-ishrafi.git")
    print("   git push -u origin main")
    
    print("\n2. Deploy on Render:")
    print("   - Go to render.com")
    print("   - Connect GitHub repository")
    print("   - Deploy automatically")
    
    print("\n3. Alternative platforms:")
    print("   - Railway: Connect GitHub and deploy")
    print("   - Vercel: For static hosting")
    print("   - Netlify: For static hosting")
    
    print("\n🌐 Your app will be available at:")
    print("   https://al-ishrafi-accounting.onrender.com")
    
    print("\n🔐 Login credentials:")
    print("   Username: admin")
    print("   Password: admin123")

if __name__ == "__main__":
    main()
