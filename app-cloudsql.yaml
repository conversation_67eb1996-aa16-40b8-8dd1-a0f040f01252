runtime: python39

# Environment variables for Cloud SQL
env_variables:
  SECRET_KEY: "al-ishrafi-production-secret-key-2025"
  DEBUG: "False"
  ALLOWED_HOSTS: "*"
  # Update this with your actual Cloud SQL connection details
  DATABASE_URL: "postgres://dbuser:YOUR_PASSWORD@//cloudsql/YOUR_PROJECT_ID:us-central1:al-ishrafi-db/accounting"
  DJANGO_SETTINGS_MODULE: "accounting_system.settings"

# Cloud SQL instances
cloud_sql_instances:
  - YOUR_PROJECT_ID:us-central1:al-ishrafi-db

# Static file handlers
handlers:
- url: /static
  static_dir: staticfiles/
  secure: always
  expiration: 1d

- url: /media
  static_dir: media/
  secure: always
  expiration: 1d

- url: /favicon\.ico
  static_files: staticfiles/images/favicon.svg
  upload: staticfiles/images/favicon\.svg
  secure: always

- url: /.*
  script: auto
  secure: always

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 20
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6
  max_concurrent_requests: 100

# Resource settings for production
resources:
  cpu: 2
  memory_gb: 1
  disk_size_gb: 20

# Health checks
readiness_check:
  path: "/"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: "/"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

# Beta features
beta_settings:
  cloud_sql_instances: YOUR_PROJECT_ID:us-central1:al-ishrafi-db
